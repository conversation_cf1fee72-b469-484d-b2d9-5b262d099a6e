// Register tsconfig paths before any other imports
import "tsconfig-paths/register";

import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import dotenv from "dotenv";
import mongoose from "mongoose";
import { rabbitMQ } from "@shared/utils/rabbitmq";

// Import routes
import courseRoutes from "./routes/course.routes";
import lessonRoutes from "./routes/lesson.routes";
import assignmentRoutes from "./routes/assignment.routes";
import progressRoutes from "./routes/progress.routes";
import coachingRoutes from "./routes/coaching.routes";

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan("combined"));
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get("/health", (_req, res) => {
  res.json({
    success: true,
    message: "Course & Learning Service is healthy",
    timestamp: new Date().toISOString(),
  });
});

// Routes
app.use("/api/courses", courseRoutes);
app.use("/api/lessons", lessonRoutes);
app.use("/api/assignments", assignmentRoutes);
app.use("/api/progress", progressRoutes);
app.use("/api/coaching", coachingRoutes);

// Error handling middleware
app.use(
  (
    err: any,
    _req: express.Request,
    res: express.Response,
    _next: express.NextFunction
  ) => {
    console.error("Error:", err);
    res.status(err.status || 500).json({
      success: false,
      message: err.message || "Internal Server Error",
      ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
    });
  }
);

// 404 handler
app.use("*", (_req, res) => {
  res.status(404).json({
    success: false,
    message: "Endpoint not found",
  });
});

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(
      process.env.MONGODB_URI || "mongodb://localhost:27017/timecourse_courses"
    );
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error("Database connection failed:", error);
    process.exit(1);
  }
};

// Start server
const startServer = async () => {
  try {
    await connectDB();
    await rabbitMQ.connect();

    // Subscribe to events
    await rabbitMQ.subscribeToEvents(
      "user.events",
      "course.user.events",
      ["user.created", "user.updated"],
      async (data) => {
        console.log("Received user event:", data.type);
        // Handle user events here
      }
    );

    app.listen(PORT, () => {
      console.log(`Course & Learning Service running on port ${PORT}`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
};

// Graceful shutdown
process.on("SIGTERM", async () => {
  console.log("SIGTERM received, shutting down gracefully");
  await rabbitMQ.close();
  await mongoose.connection.close();
  process.exit(0);
});

process.on("SIGINT", async () => {
  console.log("SIGINT received, shutting down gracefully");
  await rabbitMQ.close();
  await mongoose.connection.close();
  process.exit(0);
});

startServer();
