import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../../../shared/middleware/auth';
export declare class TutorController {
    private createTutorSchema;
    createTutor(req: AuthenticatedRequest, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    getAllTutors(req: Request, res: Response): Promise<void>;
    approveTutor(req: AuthenticatedRequest, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    deactivateTutor(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
}
//# sourceMappingURL=tutor.controller.d.ts.map