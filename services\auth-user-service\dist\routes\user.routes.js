"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const user_controller_1 = require("../controllers/user.controller");
const auth_1 = require("../../../shared/middleware/auth");
const router = express_1.default.Router();
const userController = new user_controller_1.UserController();
router.get('/', auth_1.authenticateToken, (0, auth_1.requireRole)(['admin']), userController.getAllUsers);
router.get('/:id', auth_1.authenticateToken, (0, auth_1.requireRole)(['admin']), userController.getUserById);
router.put('/:id', auth_1.authenticateToken, (0, auth_1.requireRole)(['admin']), userController.updateUser);
router.delete('/:id', auth_1.authenticateToken, (0, auth_1.requireRole)(['admin']), userController.deleteUser);
router.get('/students/failed', auth_1.authenticateToken, (0, auth_1.requireRole)(['admin', 'tutor']), userController.getFailedStudents);
exports.default = router;
//# sourceMappingURL=user.routes.js.map