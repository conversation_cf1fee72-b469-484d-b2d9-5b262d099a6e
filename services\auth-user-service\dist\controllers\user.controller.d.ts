import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../../../shared/middleware/auth';
export declare class UserController {
    getAllUsers(req: AuthenticatedRequest, res: Response): Promise<void>;
    getUserById(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    updateUser(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    deleteUser(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    getFailedStudents(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=user.controller.d.ts.map