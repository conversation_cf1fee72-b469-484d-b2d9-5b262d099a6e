"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TutorController = void 0;
const User_model_1 = require("../models/User.model");
const joi_1 = __importDefault(require("joi"));
class TutorController {
    constructor() {
        this.createTutorSchema = joi_1.default.object({
            firebaseUid: joi_1.default.string().required(),
            email: joi_1.default.string().email().required(),
            displayName: joi_1.default.string().min(2).max(100).required(),
            photoURL: joi_1.default.string().uri().optional(),
        });
    }
    async createTutor(req, res) {
        try {
            const { error, value } = this.createTutorSchema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation error',
                    errors: error.details.map(detail => detail.message)
                });
            }
            const { firebaseUid, email, displayName, photoURL } = value;
            const existingUser = await User_model_1.UserModel.findOne({
                $or: [{ firebaseUid }, { email }]
            });
            if (existingUser) {
                return res.status(409).json({
                    success: false,
                    message: 'User already exists'
                });
            }
            const tutor = new User_model_1.UserModel({
                firebaseUid,
                email,
                displayName,
                photoURL,
                role: 'tutor',
                isActive: true,
                approvedBy: req.user.uid,
                approvalDate: new Date(),
            });
            await tutor.save();
            res.status(201).json({
                success: true,
                message: 'Tutor account created successfully',
                data: tutor
            });
        }
        catch (error) {
            console.error('Create tutor error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create tutor account',
                errors: [error.message]
            });
        }
    }
    async getAllTutors(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const search = req.query.search;
            const filter = { role: 'tutor' };
            if (search) {
                filter.$or = [
                    { displayName: { $regex: search, $options: 'i' } },
                    { email: { $regex: search, $options: 'i' } }
                ];
            }
            const skip = (page - 1) * limit;
            const [tutors, total] = await Promise.all([
                User_model_1.UserModel.find(filter)
                    .skip(skip)
                    .limit(limit)
                    .sort({ createdAt: -1 })
                    .populate('approvedBy', 'displayName email'),
                User_model_1.UserModel.countDocuments(filter)
            ]);
            const response = {
                success: true,
                message: 'Tutors retrieved successfully',
                data: tutors,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('Get all tutors error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve tutors',
                errors: [error.message]
            });
        }
    }
    async approveTutor(req, res) {
        try {
            const tutor = await User_model_1.UserModel.findByIdAndUpdate(req.params.id, {
                $set: {
                    isActive: true,
                    approvedBy: req.user.uid,
                    approvalDate: new Date()
                }
            }, { new: true, runValidators: true });
            if (!tutor || tutor.role !== 'tutor') {
                return res.status(404).json({
                    success: false,
                    message: 'Tutor not found'
                });
            }
            res.json({
                success: true,
                message: 'Tutor approved successfully',
                data: tutor
            });
        }
        catch (error) {
            console.error('Approve tutor error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to approve tutor',
                errors: [error.message]
            });
        }
    }
    async deactivateTutor(req, res) {
        try {
            const tutor = await User_model_1.UserModel.findByIdAndUpdate(req.params.id, { $set: { isActive: false } }, { new: true });
            if (!tutor || tutor.role !== 'tutor') {
                return res.status(404).json({
                    success: false,
                    message: 'Tutor not found'
                });
            }
            res.json({
                success: true,
                message: 'Tutor deactivated successfully'
            });
        }
        catch (error) {
            console.error('Deactivate tutor error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to deactivate tutor',
                errors: [error.message]
            });
        }
    }
}
exports.TutorController = TutorController;
//# sourceMappingURL=tutor.controller.js.map