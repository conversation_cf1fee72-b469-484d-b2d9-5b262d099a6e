"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const User_model_1 = require("../models/User.model");
const rabbitmq_1 = require("../../../shared/utils/rabbitmq");
const joi_1 = __importDefault(require("joi"));
class AuthController {
    constructor() {
        this.registerSchema = joi_1.default.object({
            firebaseUid: joi_1.default.string().required(),
            email: joi_1.default.string().email().required(),
            displayName: joi_1.default.string().min(2).max(100).required(),
            photoURL: joi_1.default.string().uri().optional(),
        });
    }
    async register(req, res) {
        try {
            const { error, value } = this.registerSchema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation error',
                    errors: error.details.map(detail => detail.message)
                });
            }
            const { firebaseUid, email, displayName, photoURL } = value;
            const existingUser = await User_model_1.UserModel.findOne({
                $or: [{ firebaseUid }, { email }]
            });
            if (existingUser) {
                return res.status(409).json({
                    success: false,
                    message: 'User already exists'
                });
            }
            const newUser = new User_model_1.UserModel({
                firebaseUid,
                email,
                displayName,
                photoURL,
                role: 'student',
                isActive: true,
            });
            await newUser.save();
            const userCreatedEvent = {
                type: 'USER_CREATED',
                data: newUser.toObject(),
                timestamp: new Date(),
            };
            await rabbitmq_1.rabbitMQ.publishEvent('user.events', 'user.created', userCreatedEvent);
            res.status(201).json({
                success: true,
                message: 'User registered successfully',
                data: newUser
            });
        }
        catch (error) {
            console.error('Registration error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to register user',
                errors: [error.message]
            });
        }
    }
    async login(req, res) {
        try {
            const { firebaseUid } = req.body;
            if (!firebaseUid) {
                return res.status(400).json({
                    success: false,
                    message: 'Firebase UID is required'
                });
            }
            const user = await User_model_1.UserModel.findOne({ firebaseUid, isActive: true });
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found or inactive'
                });
            }
            res.json({
                success: true,
                message: 'Login successful',
                data: user
            });
        }
        catch (error) {
            console.error('Login error:', error);
            res.status(500).json({
                success: false,
                message: 'Login failed',
                errors: [error.message]
            });
        }
    }
    async getProfile(req, res) {
        try {
            const user = await User_model_1.UserModel.findOne({ firebaseUid: req.user.uid, isActive: true })
                .populate('subscriptions');
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }
            res.json({
                success: true,
                message: 'Profile retrieved successfully',
                data: user
            });
        }
        catch (error) {
            console.error('Get profile error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve profile',
                errors: [error.message]
            });
        }
    }
    async updateProfile(req, res) {
        try {
            const updateSchema = joi_1.default.object({
                displayName: joi_1.default.string().min(2).max(100).optional(),
                photoURL: joi_1.default.string().uri().optional(),
            });
            const { error, value } = updateSchema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation error',
                    errors: error.details.map(detail => detail.message)
                });
            }
            const user = await User_model_1.UserModel.findOneAndUpdate({ firebaseUid: req.user.uid, isActive: true }, { $set: value }, { new: true, runValidators: true });
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }
            res.json({
                success: true,
                message: 'Profile updated successfully',
                data: user
            });
        }
        catch (error) {
            console.error('Update profile error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update profile',
                errors: [error.message]
            });
        }
    }
    async deleteAccount(req, res) {
        try {
            const user = await User_model_1.UserModel.findOneAndUpdate({ firebaseUid: req.user.uid }, { $set: { isActive: false } }, { new: true });
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }
            res.json({
                success: true,
                message: 'Account deactivated successfully'
            });
        }
        catch (error) {
            console.error('Delete account error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete account',
                errors: [error.message]
            });
        }
    }
}
exports.AuthController = AuthController;
//# sourceMappingURL=auth.controller.js.map