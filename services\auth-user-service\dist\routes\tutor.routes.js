"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const tutor_controller_1 = require("../controllers/tutor.controller");
const auth_1 = require("../../../shared/middleware/auth");
const router = express_1.default.Router();
const tutorController = new tutor_controller_1.TutorController();
router.post('/', auth_1.authenticateToken, (0, auth_1.requireRole)(['admin']), tutorController.createTutor);
router.get('/', auth_1.authenticateToken, (0, auth_1.requireRole)(['admin']), tutorController.getAllTutors);
router.put('/:id/approve', auth_1.authenticateToken, (0, auth_1.requireRole)(['admin']), tutorController.approveTutor);
router.put('/:id/deactivate', auth_1.authenticateToken, (0, auth_1.requireRole)(['admin']), tutorController.deactivateTutor);
exports.default = router;
//# sourceMappingURL=tutor.routes.js.map