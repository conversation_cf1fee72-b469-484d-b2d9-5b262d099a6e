"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_controller_1 = require("../controllers/auth.controller");
const auth_1 = require("../../../shared/middleware/auth");
const router = express_1.default.Router();
const authController = new auth_controller_1.AuthController();
router.post('/register', authController.register);
router.post('/login', authController.login);
router.get('/profile', auth_1.authenticateToken, authController.getProfile);
router.put('/profile', auth_1.authenticateToken, authController.updateProfile);
router.delete('/account', auth_1.authenticateToken, authController.deleteAccount);
exports.default = router;
//# sourceMappingURL=auth.routes.js.map