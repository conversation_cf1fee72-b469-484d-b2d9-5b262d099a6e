"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const User_model_1 = require("../models/User.model");
class UserController {
    async getAllUsers(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const role = req.query.role;
            const search = req.query.search;
            const filter = {};
            if (role) {
                filter.role = role;
            }
            if (search) {
                filter.$or = [
                    { displayName: { $regex: search, $options: 'i' } },
                    { email: { $regex: search, $options: 'i' } }
                ];
            }
            const skip = (page - 1) * limit;
            const [users, total] = await Promise.all([
                User_model_1.UserModel.find(filter)
                    .skip(skip)
                    .limit(limit)
                    .sort({ createdAt: -1 }),
                User_model_1.UserModel.countDocuments(filter)
            ]);
            const response = {
                success: true,
                message: 'Users retrieved successfully',
                data: users,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('Get all users error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve users',
                errors: [error.message]
            });
        }
    }
    async getUserById(req, res) {
        try {
            const user = await User_model_1.UserModel.findById(req.params.id)
                .populate('subscriptions');
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }
            res.json({
                success: true,
                message: 'User retrieved successfully',
                data: user
            });
        }
        catch (error) {
            console.error('Get user by ID error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve user',
                errors: [error.message]
            });
        }
    }
    async updateUser(req, res) {
        try {
            const { isActive, role } = req.body;
            const user = await User_model_1.UserModel.findByIdAndUpdate(req.params.id, { $set: { isActive, role } }, { new: true, runValidators: true });
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }
            res.json({
                success: true,
                message: 'User updated successfully',
                data: user
            });
        }
        catch (error) {
            console.error('Update user error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update user',
                errors: [error.message]
            });
        }
    }
    async deleteUser(req, res) {
        try {
            const user = await User_model_1.UserModel.findByIdAndUpdate(req.params.id, { $set: { isActive: false } }, { new: true });
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }
            res.json({
                success: true,
                message: 'User deactivated successfully'
            });
        }
        catch (error) {
            console.error('Delete user error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete user',
                errors: [error.message]
            });
        }
    }
    async getFailedStudents(req, res) {
        try {
            res.json({
                success: true,
                message: 'Failed students retrieved successfully',
                data: []
            });
        }
        catch (error) {
            console.error('Get failed students error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve failed students',
                errors: [error.message]
            });
        }
    }
}
exports.UserController = UserController;
//# sourceMappingURL=user.controller.js.map